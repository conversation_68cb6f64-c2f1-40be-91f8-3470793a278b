# db_operations.py

from sqlalchemy.orm import Session
from sqlalchemy import desc, distinct, asc
from sqlalchemy import func, and_, text
from typing import Optional, List, Dict
from datetime import datetime, timezone, date, timedelta
from models import (
    SessionModel,
    UploadedFile,
    CalculationResult,
    ErrorCount,
    ErrorReason,
    PartsCalculationResult,
    PartsErrorCount,
    PartsUploadedFile,
    MachineTypeMaterialInfo,
    DailyPartsCalculationResult,
    DailyPartsErrorCount,
    AirlineConnectorInfo,
    # DailyPartsUploadedFile,
    BomPartNote,
    BomReverseMapping,
    MaterialFilterRule,
)
from core.constants import STAGES


def create_new_session(db: Session, user_id: str) -> SessionModel:
    """
    创建新的会话记录。

    :param db: 数据库会话
    :param user_id: 用户ID
    :param ip_address: 用户IP地址
    :param user_agent: 用户代理字符串
    :return: 新创建的SessionModel实例
    """
    new_session = SessionModel(
        user_id=user_id,
    )
    db.add(new_session)
    db.commit()
    db.refresh(new_session)
    return new_session


def update_session_status(
    db: Session, session_id: str, status: str
) -> Optional[SessionModel]:
    """
    更新会话erp_number。

    :param db: 数据库会话
    :param session_id: 会话ID
    :param status: status
    :return: 更新后的SessionModel实例，如果不存在则返回None
    """
    session = (
        db.query(SessionModel).filter(SessionModel.id == session_id).first()
    )
    if session:
        session.status = status
        db.commit()
    return session


def update_session_erp_number(
    db: Session, session_id: str, erp_number: str
) -> Optional[SessionModel]:
    """
    更新会话erp_number。

    :param db: 数据库会话
    :param session_id: 会话ID
    :param erp_number: erp_number
    :return: 更新后的SessionModel实例，如果不存在则返回None
    """
    session = (
        db.query(SessionModel).filter(SessionModel.id == session_id).first()
    )
    if session:
        session.erp_number = erp_number
        db.commit()
    return session


def update_session_activity(
    db: Session, session_id: str
) -> Optional[SessionModel]:
    """
    更新会话的最后活动时间。

    :param db: 数据库会话
    :param session_id: 会话ID
    :return: 更新后的SessionModel实例，如果不存在则返回None
    """
    session = (
        db.query(SessionModel).filter(SessionModel.id == session_id).first()
    )
    if session:
        session.last_activity = datetime.now(timezone.utc)
        db.commit()
    return session


def add_uploaded_file(
    db: Session,
    session_id: str,
    filename: str,
    file_path: str,
    file_type: str,
    file_size: int,
) -> UploadedFile:
    """
    添加上传文件记录。

    :param db: 数据库会话
    :param session_id: 会话ID
    :param filename: 文件名
    :param file_path: 文件路径
    :param file_type: 文件类型
    :param file_size: 文件大小（字节）
    :return: 新创建的UploadedFile实例
    """
    new_file = UploadedFile(
        session_id=session_id,
        filename=filename,
        file_path=file_path,
        file_type=file_type,
        file_size=file_size,
    )
    db.add(new_file)
    db.commit()
    db.refresh(new_file)
    return new_file


def add_calculation_result(
    db: Session, session_id: str, calculation_type: str, result_data: dict
) -> CalculationResult:
    """
    Adds a calculation result and updates is_latest flags.
    Only updates records for the same ERP number.

    :param db: Database session
    :param session_id: Session ID
    :param calculation_type: Calculation type
    :param result_data: Result data dictionary
    :return: CalculationResult instance
    """
    # Get session and ERP number
    session = (
        db.query(SessionModel).filter(SessionModel.id == session_id).first()
    )
    if not session:
        raise ValueError('Session not found')

    erp_number = session.erp_number

    # Get all sessions for this ERP number
    erp_sessions = (
        db.query(SessionModel.id)
        .filter(SessionModel.erp_number == erp_number)
        .all()
    )
    erp_session_ids = [s.id for s in erp_sessions]

    # Set is_latest=False only for records of the same ERP number
    db.query(CalculationResult).filter(
        CalculationResult.calculation_type == calculation_type,
        CalculationResult.session_id.in_(erp_session_ids),
    ).update({'is_latest': False})

    # Check if record exists for this session and type
    record = (
        db.query(CalculationResult)
        .filter(
            CalculationResult.session_id == session_id,
            CalculationResult.calculation_type == calculation_type,
        )
        .first()
    )

    if record:
        # Update existing record
        record.result_file_path = result_data
        record.is_latest = True
        result = record
    else:
        # Create new result
        result = CalculationResult(
            session_id=session_id,
            calculation_type=calculation_type,
            result_file_path=result_data,
            is_latest=True,
        )
        db.add(result)

    db.commit()
    return result


def get_session_by_id(db: Session, session_id: str) -> Optional[SessionModel]:
    """
    通过ID获取会话。

    :param db: 数据库会话
    :param session_id: 会话ID
    :return: SessionModel实例，如果不存在则返回None
    """
    return db.query(SessionModel).filter(SessionModel.id == session_id).first()


def get_latest_calculation_session(
    db: Session, erp_number: str
) -> SessionModel:
    """
    获取指定用户和ERP编号的最新计算结果文件。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :return: 最新的CalculationResult列表
    """
    # 获取该用户对应ERP的最新completed session
    latest_session = (
        db.query(SessionModel)
        .filter(
            SessionModel.erp_number == erp_number,
            SessionModel.status == 'completed',
        )
        .order_by(desc(SessionModel.last_activity))
        .first()
    )

    return latest_session


def get_uploaded_files_by_session(
    db: Session, session_id: str
) -> list[UploadedFile]:
    """
    获取指定会话的所有上传文件,每个file_type只返回id最大的一条记录。

    :param db: 数据库会话
    :param session_id: 会话ID
    :return: UploadedFile实例列表
    """
    # 使用子查询找出每个file_type最大id的记录
    latest_files_subquery = (
        db.query(
            UploadedFile.file_type,
            func.max(UploadedFile.id).label('max_id'),
        )
        .filter(UploadedFile.session_id == session_id)
        .group_by(UploadedFile.file_type)
        .subquery()
    )

    # 主查询关联子查询获取完整的记录
    return (
        db.query(UploadedFile)
        .join(
            latest_files_subquery,
            and_(
                UploadedFile.file_type == latest_files_subquery.c.file_type,
                UploadedFile.id == latest_files_subquery.c.max_id,
                UploadedFile.session_id == session_id,
            ),
        )
        .all()
    )


def get_calculation_results_by_session(
    db: Session, session_id: str
) -> list[CalculationResult]:
    """
    获取指定会话的每种计算类型的最新计算结果。

    :param db: 数据库会话
    :param session_id: 会话ID
    :return: CalculationResult实例列表，每种calculation_type只返回最新的一条
    """
    # 使用子查询找到每种类型的最新记录
    latest_results = (
        db.query(
            CalculationResult.calculation_type,
            func.max(CalculationResult.id).label('max_id'),
        )
        .filter(
            CalculationResult.session_id == session_id,
            ~CalculationResult.result_file_path.endswith('.txt'),
        )
        .group_by(CalculationResult.calculation_type)
        .subquery()
    )

    # 主查询关联子查询获取完整记录
    return (
        db.query(CalculationResult)
        .join(
            latest_results,
            and_(
                CalculationResult.calculation_type
                == latest_results.c.calculation_type,
                CalculationResult.id == latest_results.c.max_id,
            ),
        )
        .filter(CalculationResult.session_id == session_id)
        .all()
    )


def get_sessions(
    db: Session,
    user_id: Optional[str] = None,
    erp_number: Optional[str] = None,
    status: Optional[str] = None,
) -> List[SessionModel]:
    """
    获取符合条件的会话列表。

    :param db: 数据库会话
    :param user_id: 用户ID（可选）
    :param erp_number: ERP编号（可选）
    :param status: 会话状态（可选）
    :return: 符合条件的SessionModel列表
    """
    query = db.query(SessionModel)
    if user_id:
        query = query.filter(SessionModel.user_id == user_id)
    if erp_number:
        query = query.filter(SessionModel.erp_number == erp_number)
    if status:
        query = query.filter(SessionModel.status == status)
    return query.all()


def add_error_count(
    db: Session,
    session_id: str,
    cal_type: str,
    error_count: int,
    total_count: int,
) -> ErrorCount:
    """
    Adds or updates error count and manages is_latest flags.
    Only updates records for the same ERP number.

    :param db: Database session
    :param session_id: Session ID
    :param cal_type: Calculation type
    :param error_count: Error count
    :param total_count: Total count
    :return: ErrorCount instance
    """
    # Get session and ERP number
    session = (
        db.query(SessionModel).filter(SessionModel.id == session_id).first()
    )
    if not session:
        raise ValueError('Session not found')

    erp_number = session.erp_number

    # Get all sessions for this ERP number
    erp_sessions = (
        db.query(SessionModel.id)
        .filter(SessionModel.erp_number == erp_number)
        .all()
    )
    erp_session_ids = [s.id for s in erp_sessions]

    # Set is_latest=False only for records of the same ERP number
    db.query(ErrorCount).filter(
        ErrorCount.cal_type == cal_type,
        ErrorCount.session_id.in_(erp_session_ids),
    ).update({'is_latest': False})

    # Check for existing record
    existing_record = (
        db.query(ErrorCount)
        .filter(
            ErrorCount.session_id == session_id,
            ErrorCount.cal_type == cal_type,
        )
        .first()
    )

    if existing_record:
        existing_record.error_count = error_count
        existing_record.total_count = total_count
        existing_record.is_latest = True
        result = existing_record
    else:
        result = ErrorCount(
            session_id=session_id,
            cal_type=cal_type,
            error_count=error_count,
            total_count=total_count,
            is_latest=True,
        )
        db.add(result)

    db.commit()
    return result


def update_error_count(
    db: Session,
    session_id: str,
    cal_type: str,
    error_count: Optional[int] = None,
    total_count: Optional[int] = None,
    manual_confirm: Optional[bool] = None,
) -> Optional[ErrorCount]:
    """
    更新错误计数记录。

    :param db: 数据库会话
    :param session_id: 会话ID
    :param file_name: 文件名
    :param error_count: 新的错误数量
    :return: 更新后的ErrorCount实例，如果不存在则返回None
    """
    error_count_record = (
        db.query(ErrorCount)
        .filter(
            ErrorCount.session_id == session_id,
            ErrorCount.cal_type == cal_type,
        )
        .first()
    )
    if error_count_record:
        if error_count is not None:
            error_count_record.error_count = error_count
        if total_count is not None:
            error_count_record.total_count = total_count
        if manual_confirm is not None:
            error_count_record.manual_confirm = manual_confirm
        error_count_record.create_time = datetime.utcnow()
        db.commit()
        db.refresh(error_count_record)
    return error_count_record


def get_error_counts_by_session(
    db: Session, session_id: str
) -> List[ErrorCount]:
    """
    获取指定会话的所有错误计数记录。

    :param db: 数据库会话
    :param session_id: 会话ID
    :return: ErrorCount实例列表
    """
    return (
        db.query(ErrorCount).filter(ErrorCount.session_id == session_id).all()
    )


def get_error_count(
    db: Session, session_id: str, cal_type: str
) -> Optional[ErrorCount]:
    """
    获取指定会话和文件的错误计数记录。

    :param db: 数据库会话
    :param session_id: 会话ID
    :param cal_type: 文件类型
    :return: ErrorCount实例，如果不存在则返回None
    """
    return (
        db.query(ErrorCount)
        .filter(
            ErrorCount.session_id == session_id,
            ErrorCount.cal_type == cal_type,
        )
        .first()
    )


def get_erp_calculation_count(db: Session, erp_number: str) -> int:
    """
    获取指定ERP编号的计算次数。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :return: 计算次数
    """
    return (
        db.query(SessionModel)
        .filter(
            SessionModel.erp_number == erp_number,
            SessionModel.status == 'completed',
        )
        .count()
    )


def get_latest_erp_calculation_errors(
    db: Session, erp_number: str
) -> Optional[dict]:
    """
    获取指定ERP编号最新计算的错误次数、总数和比例。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :return: 元组 (总错误数, 总行数, 错误比例)
    """
    # 获取最新的会话
    latest_session = (
        db.query(SessionModel)
        .filter(SessionModel.erp_number == erp_number)
        .order_by(desc(SessionModel.created_at))
        .first()
    )

    if not latest_session:
        return None

    # 获取该会话的所有错误计数
    error_counts = (
        db.query(ErrorCount)
        .filter(ErrorCount.session_id == latest_session.id)
        .all()
    )

    res = []
    for ec in error_counts:
        res.append(ec.to_dict())

    total_errors = sum(ec.error_count for ec in error_counts)
    total_count = sum(ec.total_count for ec in error_counts)
    manual_confirm = all(ec.manual_confirm for ec in error_counts)

    res.append(
        {
            'type': '总计',
            'error_count': total_errors,
            'total_count': total_count,
            'manual_confirm': manual_confirm,
        }
    )

    return res


def get_erp_calculation_history(db: Session, erp_number: str) -> List[dict]:
    """
    获取指定ERP编号的计算历史，包括每次计算的详细错误统计。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :return: 包含计算历史的字典列表
    """
    sessions = (
        db.query(SessionModel)
        .filter(
            SessionModel.erp_number == erp_number,
            SessionModel.status == 'completed',
        )
        .order_by(desc(SessionModel.created_at))
        .all()
    )

    history = []
    for session in sessions:
        error_counts = (
            db.query(ErrorCount)
            .filter(ErrorCount.session_id == session.id)
            .all()
        )

        if error_counts:

            session_errors = []
            for ec in error_counts:
                session_errors.append(ec.to_dict())

            total_errors = sum(ec.error_count for ec in error_counts)
            total_count = sum(ec.total_count for ec in error_counts)
            manual_confirm = all(ec.manual_confirm for ec in error_counts)

            session_errors.append(
                {
                    'type': '总计',
                    'error_count': total_errors,
                    'total_count': total_count,
                    'manual_confirm': manual_confirm,
                }
            )

            history.append(
                {
                    'session_id': session.id,
                    'calculation_time': session.created_at,
                    'error_details': session_errors,
                }
            )

    return history


def get_all_erp_latest_statistics(db: Session):
    """
    Get the latest statistics for all ERPs with detailed error breakdowns using SQLAlchemy ORM
    Returns aggregated results including calculation counts and latest error statistics

    :param db: Database session
    :return: List of dictionaries containing ERP statistics
    """
    from sqlalchemy import func

    # Get all unique ERPs with completed calculations and their counts
    erp_stats = (
        db.query(
            SessionModel.erp_number,
            func.count(SessionModel.id).label('total_calculations'),
            func.max(SessionModel.created_at).label('latest_calculation_date'),
        )
        .filter(
            SessionModel.erp_number.isnot(None),
            SessionModel.status == 'completed',
        )
        .group_by(SessionModel.erp_number)
        .all()
    )

    results = []
    for erp in erp_stats:
        # Get the latest completed session for this ERP
        latest_sessions = (
            db.query(SessionModel)
            .filter(
                SessionModel.erp_number == erp.erp_number,
                SessionModel.status == 'completed',
            )
            .order_by(SessionModel.last_activity.desc())
            .all()
        )

        for latest_session in latest_sessions:
            # Get error counts for the latest session using relationship
            error_counts = latest_session.error_counts

            if not error_counts:
                continue

            # Calculate error statistics
            error_details = []
            total_errors = 0
            total_count = 0
            manual_confirms = []
            stage_counts = {i: None for i in STAGES.keys()}

            for ec in error_counts:
                error_details.append(
                    {
                        'type': ec.cal_type,
                        'error_count': ec.error_count,
                        'total_count': ec.total_count,
                        'manual_confirm': ec.manual_confirm,
                        'create_time': ec.create_time.isoformat()
                        if ec.create_time
                        else None,
                    }
                )
                total_errors += ec.error_count
                total_count += ec.total_count
                manual_confirms.append(ec.manual_confirm)

                for stage, stage_list in STAGES.items():
                    if ec.cal_type in stage_list:
                        if stage_counts[stage] is None:
                            stage_counts[stage] = {
                                'error_count': ec.error_count,
                                'total_count': ec.total_count,
                            }
                        else:
                            stage_counts[stage][
                                'error_count'
                            ] += ec.error_count
                            stage_counts[stage][
                                'total_count'
                            ] += ec.total_count

                        break
            # Add total summary
            error_details.append(
                {
                    'type': '总计',
                    'error_count': total_errors,
                    'total_count': total_count,
                    'manual_confirm': all(manual_confirms)
                    if manual_confirms
                    else False,
                    'create_time': latest_session.created_at.isoformat()
                    if latest_session.created_at
                    else None,
                }
            )
            for stage, count in stage_counts.items():
                if count:
                    error_details.append(
                        {
                            'type': f'stage{stage}',
                            'error_count': count['error_count'],
                            'total_count': count['total_count'],
                            'manual_confirm': False,
                            'create_time': latest_session.created_at.isoformat()
                            if latest_session.created_at
                            else None,
                        }
                    )

            results.append(
                {
                    'erp_number': erp.erp_number,
                    'total_calculations': erp.total_calculations,
                    'latest_calculation_date': erp.latest_calculation_date.isoformat()
                    if erp.latest_calculation_date
                    else None,
                    'latest_calculation': {
                        'session_id': latest_session.id,
                        'user_id': latest_session.user_id,
                        'created_at': latest_session.created_at.isoformat()
                        if latest_session.created_at
                        else None,
                        'status': latest_session.status,
                        'error_details': error_details,
                    },
                }
            )
            break

    return results


def create_error_reason(
    db: Session, erp_number: int, comment: str, user_id
) -> ErrorReason:
    error_reason = ErrorReason(
        erp_number=erp_number, comment=comment, create_user=user_id
    )
    db.add(error_reason)
    db.commit()
    db.refresh(error_reason)
    return error_reason


def get_error_reasons(
    db: Session,
    erp_number: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
):
    query = db.query(ErrorReason)
    if erp_number is not None:
        query = query.filter(ErrorReason.erp_number == erp_number)
    return query.offset(skip).limit(limit).all()


def get_error_reason(db: Session, reason_id: int):
    return db.query(ErrorReason).filter(ErrorReason.id == reason_id).first()


def update_error_reason(
    db: Session, reason_id: int, comment: str, user_id
) -> Optional[ErrorReason]:
    error_reason = (
        db.query(ErrorReason).filter(ErrorReason.id == reason_id).first()
    )
    if error_reason:
        error_reason.comment = comment
        error_reason.create_user = user_id
        db.commit()
        db.refresh(error_reason)
    return error_reason


def delete_error_reason(db: Session, reason_id: int, user_id) -> bool:
    error_reason = (
        db.query(ErrorReason).filter(ErrorReason.id == reason_id).first()
    )
    if error_reason:
        db.delete(error_reason)
        db.commit()
        return True
    return False


def add_parts_uploaded_file(
    db: Session,
    erp_number: str,
    part_id: str,
    file_path: str,
    file_name: str,
    file_size: int,
) -> PartsUploadedFile:
    """
    添加或更新零件上传文件记录。
    如果指定的erp_number和part_id组合已存在，则更新记录；
    否则创建新记录。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :param part_id: 零件ID
    :param file_path: 文件路径
    :param file_name: 文件名
    :param file_size: 文件大小（字节）
    :return: 新创建或更新的PartsUploadedFile实例
    """
    # 查找是否存在匹配的记录
    existing_file = (
        db.query(PartsUploadedFile)
        .filter(
            PartsUploadedFile.erp_number == erp_number,
            PartsUploadedFile.part_id == part_id,
        )
        .first()
    )

    if existing_file:
        # 如果记录存在，更新相关字段
        existing_file.file_path = file_path
        existing_file.file_name = file_name
        existing_file.file_size = file_size
        existing_file.upload_time = datetime.now(timezone.utc)  # 更新上传时间
        db.commit()
        db.refresh(existing_file)
        return existing_file
    else:
        # 如果记录不存在，创建新记录
        new_file = PartsUploadedFile(
            erp_number=erp_number,
            part_id=part_id,
            file_path=file_path,
            file_name=file_name,
            file_size=file_size,
        )
        db.add(new_file)
        db.commit()
        db.refresh(new_file)
        return new_file


def add_parts_calculation_result(
    db: Session,
    erp_number: str,
    part_id: str,
    result_file_path: str,
) -> PartsCalculationResult:
    """
    添加或更新零件计算结果记录。
    如果指定的erp_number和part_id组合已存在，则更新记录；
    否则创建新记录。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :param part_id: 零件ID
    :param result_file_path: 结果文件路径
    :return: 新创建或更新的PartsCalculationResult实例
    """
    # 查找是否存在匹配的记录
    existing_result = (
        db.query(PartsCalculationResult)
        .filter(
            PartsCalculationResult.erp_number == erp_number,
            PartsCalculationResult.part_id == part_id,
        )
        .first()
    )

    if existing_result:
        # 如果记录存在，更新相关字段
        existing_result.result_file_path = result_file_path
        existing_result.calculation_time = datetime.now(timezone.utc)  # 更新计算时间
        db.commit()
        db.refresh(existing_result)
        return existing_result
    else:
        # 如果记录不存在，创建新记录
        new_result = PartsCalculationResult(
            erp_number=erp_number,
            part_id=part_id,
            result_file_path=result_file_path,
        )
        db.add(new_result)
        db.commit()
        db.refresh(new_result)
        return new_result


def add_parts_error_count(
    db: Session,
    erp_number: str,
    part_id: str,
    error_count: int,
    total_count: int,
    cal_type: str = '',
) -> PartsErrorCount:
    """
    添加或更新零件错误计数记录。
    如果指定的erp_number和part_id组合已存在，则更新记录；
    否则创建新记录。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :param part_id: 零件ID
    :param error_count: 错误数量
    :param total_count: 总数量
    :return: 新创建或更新的PartsErrorCount实例
    """
    # 查找是否存在匹配的记录
    if cal_type:
        existing_error_count = (
            db.query(PartsErrorCount)
            .filter(
                PartsErrorCount.erp_number == erp_number,
                PartsErrorCount.part_id == part_id,
                PartsErrorCount.cal_type == cal_type,
            )
            .first()
        )
    else:
        existing_error_count = (
            db.query(PartsErrorCount)
            .filter(
                PartsErrorCount.erp_number == erp_number,
                PartsErrorCount.part_id == part_id,
                PartsErrorCount.cal_type != 'reverse',
            )
            .first()
        )

    if existing_error_count:
        # 如果记录存在，更新相关字段
        existing_error_count.error_count = error_count
        existing_error_count.total_count = total_count
        existing_error_count.create_time = datetime.now(timezone.utc)  # 更新创建时间
        existing_error_count.cal_type = cal_type
        db.commit()
        db.refresh(existing_error_count)
        return existing_error_count
    else:
        # 如果记录不存在，创建新记录
        new_error_count = PartsErrorCount(
            erp_number=erp_number,
            part_id=part_id,
            error_count=error_count,
            total_count=total_count,
            cal_type=cal_type,
        )
        db.add(new_error_count)
        db.commit()
        db.refresh(new_error_count)
        return new_error_count


def get_latest_parts_calculation(
    db: Session,
    erp_number: str,
    part_id: str,
) -> Optional[PartsCalculationResult]:
    """
    获取指定ERP和零件的最新计算结果。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :param part_id: 零件ID
    :return: 最新的PartsCalculationResult实例或None
    """
    return (
        db.query(PartsCalculationResult)
        .filter(
            PartsCalculationResult.erp_number == erp_number,
            PartsCalculationResult.part_id == part_id,
        )
        .first()
    )


def get_latest_parts_error_count(
    db: Session,
    erp_number: str,
    part_id: str,
    is_reverse: bool = False,
) -> Optional[PartsErrorCount]:
    """
    获取指定ERP和零件的最新错误统计。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :param part_id: 零件ID
    :return: 最新的PartsErrorCount实例或None
    """
    return (
        db.query(PartsErrorCount)
        .filter(
            PartsErrorCount.erp_number == erp_number,
            PartsErrorCount.part_id == part_id,
            PartsErrorCount.cal_type == 'reverse'
            if is_reverse
            else PartsErrorCount.cal_type != 'reverse',
        )
        .first()
    )


def get_parts_uploaded_files(
    db: Session,
    erp_number: str,
    part_id: str,
) -> List[PartsUploadedFile]:
    """
    获取指定ERP和零件的上传文件记录。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :param part_id: 零件ID
    :param limit: 返回记录的最大数量
    :return: PartsUploadedFile实例列表
    """
    return (
        db.query(PartsUploadedFile)
        .filter(
            PartsUploadedFile.erp_number == erp_number,
            PartsUploadedFile.part_id == part_id,
        )
        .first()
    )


def get_parts_error_summary(
    db: Session,
    erp_number: Optional[str] = None,
    is_reverse: bool = False,
) -> List[Dict]:
    """
    获取零件错误统计汇总。
    如果指定了erp_number，则只返回该ERP的统计信息。

    :param db: 数据库会话
    :param erp_number: ERP编号（可选）
    :return: 零件错误统计列表
    """
    try:
        # 基础查询
        query = db.query(PartsErrorCount)

        # 如果指定了erp_number，添加过滤条件
        if erp_number:
            query = query.filter(
                PartsErrorCount.erp_number == erp_number,
            )

        if is_reverse:
            query = query.filter(PartsErrorCount.cal_type == 'reverse')
        else:
            query = query.filter(PartsErrorCount.cal_type != 'reverse')

        query = query.filter(PartsErrorCount.erp_number != '').filter(
            PartsErrorCount.part_id != ''
        )

        # 获取每个erp_number和part_id组合的最新记录
        latest_records = (
            query.distinct(PartsErrorCount.erp_number, PartsErrorCount.part_id)
            .order_by(
                PartsErrorCount.erp_number,
                PartsErrorCount.part_id,
                desc(PartsErrorCount.create_time),
            )
            .all()
        )

        # 转换为字典列表
        result = []
        for record in latest_records:
            result.append(
                {
                    'erp_number': record.erp_number,
                    'part_id': record.part_id,
                    'error_count': record.error_count,
                    'total_count': record.total_count,
                    'create_time': record.create_time.isoformat()
                    if record.create_time
                    else None,
                    'cal_type': record.cal_type,
                }
            )

        return result

    except Exception as e:
        raise Exception(f'Failed to get parts error summary: {str(e)}')


def get_integrated_parts_data(
    db: Session,
    erp_number: Optional[str] = None,
) -> Dict:
    """
    整合零件信息和错误统计数据。

    Args:
        db: 数据库会话
        erp_number: 可选的ERP编号过滤条件

    Returns:
        Dict: 包含整合后的数据和汇总信息
    """
    try:
        # 获取机械信息数据
        mech_query = text(
            """
            SELECT DISTINCT
                erp AS erp_number,
                partNumber AS part_number
            FROM inner_erp_mech_info
            WHERE is_delete = 0
            """
            + (' AND erp = :erp_number' if erp_number else '')
        )

        mech_result = db.execute(
            mech_query, {'erp_number': erp_number} if erp_number else {}
        )
        mech_data = mech_result.fetchall()

        # 获取错误统计数据
        error_stats = get_parts_error_summary(db, erp_number)

        # 创建错误统计查找字典
        error_lookup = {
            f"{stat['erp_number']}_{stat['part_id']}": stat
            for stat in error_stats
        }

        # 整合数据
        integrated_data = {}
        total_parts = 0
        total_errors = 0
        erp_summary = {}

        for row in mech_data:
            erp = row.erp_number
            part = row.part_number

            if erp not in integrated_data:
                integrated_data[erp] = {}
                erp_summary[erp] = {
                    'total_parts': 0,
                    'total_errors': 0,
                    'error_rate': 0.0,
                }

            # 查找对应的错误统计
            error_key = f'{erp}_{part}'
            error_info = error_lookup.get(
                error_key,
                {'error_count': 0, 'total_count': 0, 'create_time': None},
            )

            integrated_data[erp][part] = {
                'error_count': error_info['error_count'],
                'total_count': error_info['total_count'],
                'last_update': error_info['create_time'],
            }

            # 更新统计信息
            total_parts += 1
            total_errors += error_info['error_count']
            erp_summary[erp]['total_parts'] += 1
            erp_summary[erp]['total_errors'] += error_info['error_count']

        # 计算每个ERP的错误率
        for erp in erp_summary:
            if erp_summary[erp]['total_parts'] > 0:
                erp_summary[erp]['error_rate'] = (
                    erp_summary[erp]['total_errors']
                    / erp_summary[erp]['total_parts']
                ) * 100

        # 生成总体汇总信息
        summary = {
            'total_erps': len(integrated_data),
            'total_parts': total_parts,
            'total_errors': total_errors,
            'overall_error_rate': (total_errors / total_parts * 100)
            if total_parts > 0
            else 0,
            'erp_summary': erp_summary,
            'last_update': datetime.now().isoformat(),
        }

        return {'integrated_data': integrated_data, 'summary': summary}

    except Exception as e:
        raise Exception(f'Failed to integrate parts data: {str(e)}')


async def get_all_erp_numbers(db: Session) -> List[str]:
    erp_numbers = (
        db.query(SessionModel.erp_number)
        .distinct()
        .filter(SessionModel.erp_number.isnot(None))  # 排除可能的空值
        .all()
    )
    return [erp[0] for erp in erp_numbers]


# 查询指定ERP号下type=2的最新上传文件
async def get_latest_type_file_by_erp(
    db: Session, erp_number: str, file_type: str
) -> Optional[UploadedFile]:
    latest_file = (
        db.query(UploadedFile)
        .join(SessionModel)  # 连接 SessionModel
        .filter(
            SessionModel.erp_number == erp_number,
            UploadedFile.file_type == file_type,  # 假设type存储在file_type字段中
        )
        .order_by(desc(UploadedFile.upload_time))  # 按上传时间降序排序
        .first()
    )
    return latest_file


async def get_latest_session_by_erp(
    db: Session, erp_number: str
) -> Optional[str]:
    latest_session = (
        db.query(SessionModel.id)
        .filter(SessionModel.erp_number == erp_number)
        .order_by(desc(SessionModel.created_at))  # 按创建时间降序排序
        .first()
    )
    return latest_session[0] if latest_session else None


async def get_erp_with_file_type_2(db: Session) -> List[str]:
    """
    获取所有包含 file_type=2 的上传文件的 ERP 编号

    :param db: 数据库会话
    :return: ERP 编号列表
    """
    # 使用子查询找到包含 file_type=2 的会话
    query = (
        db.query(distinct(SessionModel.erp_number))
        .join(UploadedFile, SessionModel.id == UploadedFile.session_id)
        .filter(
            UploadedFile.file_type == '2',
            func.length(SessionModel.erp_number) == 5,
        )
        .order_by(SessionModel.erp_number)
    )

    results = query.all()
    return [result[0] for result in results if result[0]]  # 过滤掉可能的 None 值


def get_all_machine_type_materials(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    search: Optional[str] = None,
    sort_field: str = 'id',
    sort_order: str = 'asc',
) -> List[MachineTypeMaterialInfo]:
    """获取所有机器类型材料信息，支持分页、搜索和排序"""
    query = db.query(MachineTypeMaterialInfo)

    # 搜索功能
    if search:
        query = query.filter(
            (MachineTypeMaterialInfo.machine_type.ilike(f'%{search}%'))
            | (MachineTypeMaterialInfo.material_detail.ilike(f'%{search}%'))
        )

    # 排序
    if sort_order == 'asc':
        query = query.order_by(
            asc(getattr(MachineTypeMaterialInfo, sort_field))
        )
    else:
        query = query.order_by(
            desc(getattr(MachineTypeMaterialInfo, sort_field))
        )

    return query.offset(skip).limit(limit).all()


def get_total_count(db: Session, search: Optional[str] = None) -> int:
    """获取总记录数"""
    query = db.query(MachineTypeMaterialInfo)
    if search:
        query = query.filter(
            (MachineTypeMaterialInfo.machine_type.ilike(f'%{search}%'))
            | (MachineTypeMaterialInfo.material_detail.ilike(f'%{search}%'))
        )
    return query.count()


def get_machine_type_material_by_id(
    db: Session, material_id: int
) -> Optional[MachineTypeMaterialInfo]:
    """根据ID获取特定记录"""
    return (
        db.query(MachineTypeMaterialInfo)
        .filter(MachineTypeMaterialInfo.id == material_id)
        .first()
    )


def create_machine_type_material(
    db: Session,
    machine_type: str,
    material_detail: str,
    anotation: Optional[str] = None,
) -> MachineTypeMaterialInfo:
    """创建新的机器类型材料记录"""
    db_material = MachineTypeMaterialInfo(
        machine_type=machine_type,
        material_detail=material_detail,
        anotation=anotation,
    )
    db.add(db_material)
    db.commit()
    db.refresh(db_material)
    return db_material


def update_machine_type_material(
    db: Session,
    material_id: int,
    machine_type: Optional[str] = None,
    material_detail: Optional[str] = None,
    anotation: Optional[str] = None,
) -> Optional[MachineTypeMaterialInfo]:
    """更新现有记录"""
    db_material = (
        db.query(MachineTypeMaterialInfo)
        .filter(MachineTypeMaterialInfo.id == material_id)
        .first()
    )

    if db_material:
        if machine_type is not None:
            db_material.machine_type = machine_type
        if material_detail is not None:
            db_material.material_detail = material_detail
        if anotation is not None:
            db_material.anotation = anotation

        db.commit()
        db.refresh(db_material)

    return db_material


def delete_machine_type_material(db: Session, material_id: int) -> bool:
    """删除指定记录"""
    db_material = (
        db.query(MachineTypeMaterialInfo)
        .filter(MachineTypeMaterialInfo.id == material_id)
        .first()
    )

    if db_material:
        db.delete(db_material)
        db.commit()
        return True
    return False


def get_calculation_results_by_erp(
    db: Session, erp_number: str
) -> List[CalculationResult]:
    """
    获取指定ERP号的每种计算类型的最新计算结果。

    :param db: 数据库会话
    :param erp_number: ERP编号
    :return: CalculationResult实例列表，每种calculation_type返回最新的一条
    """
    # 使用子查询找到每种计算类型的最新记录id
    latest_results = (
        db.query(
            CalculationResult.calculation_type,
            func.max(CalculationResult.id).label('max_id'),
        )
        .join(SessionModel, CalculationResult.session_id == SessionModel.id)
        .filter(
            SessionModel.erp_number == erp_number,
            ~CalculationResult.result_file_path.endswith('.txt'),  # 排除txt文件
        )
        .group_by(CalculationResult.calculation_type)
        .subquery()
    )

    # 主查询关联子查询获取完整记录
    return (
        db.query(CalculationResult)
        .join(SessionModel, CalculationResult.session_id == SessionModel.id)
        .join(
            latest_results,
            and_(
                CalculationResult.calculation_type
                == latest_results.c.calculation_type,
                CalculationResult.id == latest_results.c.max_id,
            ),
        )
        .filter(SessionModel.erp_number == erp_number)
        .all()
    )


def get_all_erp_calculation_results(
    db: Session, calculation_type: Optional[str] = None
) -> Dict[str, List[Dict]]:
    """
    获取所有ERP的每种计算类型的最新计算结果。

    :param db: 数据库会话
    :param calculation_type: 可选的计算类型过滤
    :return: 以ERP号为键，结果列表为值的字典
    """
    # 基础查询构建
    base_query = (
        db.query(
            SessionModel.erp_number,
            CalculationResult.calculation_type,
            CalculationResult.id,
            CalculationResult.result_file_path,
            CalculationResult.calculation_time,
            func.row_number()
            .over(
                partition_by=and_(
                    SessionModel.erp_number, CalculationResult.calculation_type
                ),
                order_by=desc(CalculationResult.calculation_time),
            )
            .label('rn'),
        )
        .join(SessionModel, CalculationResult.session_id == SessionModel.id)
        .filter(~CalculationResult.result_file_path.endswith('.txt'))
    )

    # 如果指定了calculation_type，添加过滤条件
    if calculation_type:
        base_query = base_query.filter(
            CalculationResult.calculation_type == calculation_type
        )

    # 使用子查询选择每个erp_number和calculation_type组合的最新记录
    subquery = base_query.subquery()
    latest_results = db.query(subquery).filter(subquery.c.rn == 1).all()

    # 组织结果数据
    results = {}
    for result in latest_results:
        erp_number = result.erp_number
        if erp_number not in results:
            results[erp_number] = []

        results[erp_number].append(
            {
                'calculation_type': result.calculation_type,
                'result_file_path': result.result_file_path,
                'calculation_time': result.calculation_time.isoformat()
                if result.calculation_time
                else None,
            }
        )

    return results


def get_all_erp_cross_session_statistics(db: Session):
    """
    Get statistics for all ERPs with detailed error breakdowns across all sessions.
    Uses get_latest_calculation_results for consistency with other endpoints.

    :param db: Database session
    :return: List of dictionaries containing ERP statistics
    """
    # Get all unique ERPs with completed calculations and their counts
    erp_stats = (
        db.query(
            SessionModel.erp_number,
            func.count(SessionModel.id).label('total_calculations'),
            func.max(SessionModel.created_at).label('latest_calculation_date'),
        )
        .filter(
            SessionModel.erp_number.isnot(None),
            SessionModel.status == 'completed',
        )
        .group_by(SessionModel.erp_number)
        .all()
    )

    results = []
    for erp in erp_stats:
        # 使用同样的函数获取最新计算结果
        error_counts = get_latest_error_counts(db, erp.erp_number)
        if not error_counts:
            continue

        error_details = []
        total_errors = 0
        total_count = 0
        manual_confirms = []
        stage_counts = {i: None for i in STAGES.keys()}

        # 处理每个最新的计算结果
        for error_count in error_counts:
            # 获取对应的session
            session = get_session_by_error_count_id(db, error_count.id)
            if not session:
                continue
            if not error_count:
                continue

            error_details.append(
                {
                    'type': error_count.cal_type,
                    'error_count': error_count.error_count,
                    'total_count': error_count.total_count,
                    'manual_confirm': error_count.manual_confirm,
                    'create_time': error_count.create_time.isoformat()
                    if error_count.create_time
                    else None,
                    'session_id': session.id,
                    'last_activity': session.last_activity.isoformat()
                    if session.last_activity
                    else None,
                }
            )

            total_errors += error_count.error_count
            total_count += error_count.total_count
            manual_confirms.append(error_count.manual_confirm)

            # 计算stage统计
            for stage, stage_list in STAGES.items():
                if error_count.cal_type in stage_list:
                    if stage_counts[stage] is None:
                        stage_counts[stage] = {
                            'error_count': error_count.error_count,
                            'total_count': error_count.total_count,
                        }
                    else:
                        stage_counts[stage][
                            'error_count'
                        ] += error_count.error_count
                        stage_counts[stage][
                            'total_count'
                        ] += error_count.total_count
                    # break

        # 添加总计
        error_details.append(
            {
                'type': '总计',
                'error_count': total_errors,
                'total_count': total_count,
                'manual_confirm': all(manual_confirms)
                if manual_confirms
                else False,
                'create_time': datetime.now().isoformat(),
            }
        )

        # 添加stage汇总
        for stage, count in stage_counts.items():
            if count:
                error_details.append(
                    {
                        'type': f'stage{stage}',
                        'error_count': count['error_count'],
                        'total_count': count['total_count'],
                        'manual_confirm': False,
                        'create_time': datetime.now().isoformat(),
                    }
                )

        # 获取最新session (从最新计算结果中的第一个对应的session)
        latest_session = None
        if error_counts:
            latest_session = get_session_by_error_count_id(
                db, error_counts[0].id
            )

        result = {
            'erp_number': erp.erp_number,
            'total_calculations': erp.total_calculations,
            'latest_calculation_date': erp.latest_calculation_date.isoformat()
            if erp.latest_calculation_date
            else None,
            'latest_calculation': {
                'session_id': latest_session.id if latest_session else None,
                'user_id': latest_session.user_id if latest_session else None,
                'created_at': latest_session.created_at.isoformat()
                if latest_session and latest_session.created_at
                else None,
                'last_activity': latest_session.last_activity.isoformat()
                if latest_session and latest_session.last_activity
                else None,
                'status': latest_session.status if latest_session else None,
                'error_details': error_details,
            },
        }

        results.append(result)

    return results


def get_latest_calculation_results(db: Session, erp_number: str):
    """
    Gets the latest calculation results using is_latest flag.

    :param db: Database session
    :param erp_number: ERP number
    :return: List of latest CalculationResult records
    """
    return (
        db.query(CalculationResult)
        .join(SessionModel, CalculationResult.session_id == SessionModel.id)
        .filter(
            SessionModel.erp_number == erp_number,
            SessionModel.status == 'completed',
            CalculationResult.result_file_path.endswith('.xlsx'),
            CalculationResult.is_latest == True,
        )
        .all()
    )


def get_latest_error_counts(db: Session, erp_number: str):
    """
    Gets the latest error counts using is_latest flag.

    :param db: Database session
    :param erp_number: ERP number
    :return: List of latest ErrorCount records
    """
    return (
        db.query(ErrorCount)
        .join(SessionModel, ErrorCount.session_id == SessionModel.id)
        .filter(
            SessionModel.erp_number == erp_number,
            SessionModel.status == 'completed',
            ErrorCount.is_latest == True,
        )
        .all()
    )


def get_session_by_result_id(db: Session, result_id: int) -> SessionModel:
    """
    Get the session associated with a calculation result

    :param db: Database session
    :param result_id: CalculationResult ID
    :return: Associated SessionModel
    """
    return (
        db.query(SessionModel)
        .join(
            CalculationResult, SessionModel.id == CalculationResult.session_id
        )
        .filter(CalculationResult.id == result_id)
        .first()
    )


def get_session_by_error_count_id(db: Session, result_id: int) -> SessionModel:
    """
    Get the session associated with a calculation result

    :param db: Database session
    :param result_id: CalculationResult ID
    :return: Associated SessionModel
    """
    return (
        db.query(SessionModel)
        .join(ErrorCount, SessionModel.id == ErrorCount.session_id)
        .filter(ErrorCount.id == result_id)
        .first()
    )


def update_session(db: Session):
    """
    Update the session's last_activity field to the current time.

    :param db: Database session
    """
    db.query(SessionModel).update(
        {SessionModel.last_activity: datetime.now(timezone.utc)}
    )
    db.commit()


def update_calculation_time(db: Session):
    """
    Update the calculation result's calculation_time field to the current time.

    :param db: Database session
    """
    db.query(CalculationResult).update(
        {CalculationResult.calculation_time: datetime.now(timezone.utc)}
    )
    db.commit()


# Get latest daily calculation result
def get_latest_daily_parts_calculation(
    db: Session,
    erp_number: str,
    part_id: str,
    specific_date: Optional[date] = None,
) -> Optional[DailyPartsCalculationResult]:
    """
    Get the latest daily calculation result for a specific ERP and part ID.
    If specific_date is provided, get result for that date, otherwise get the most recent.

    :param db: Database session
    :param erp_number: ERP number
    :param part_id: Part ID
    :param specific_date: Optional specific date to retrieve
    :return: DailyPartsCalculationResult or None
    """
    query = db.query(DailyPartsCalculationResult).filter(
        DailyPartsCalculationResult.erp_number == erp_number,
        DailyPartsCalculationResult.part_id == part_id,
    )

    if specific_date:
        query = query.filter(
            DailyPartsCalculationResult.calculation_date == specific_date
        )
        return query.first()
    else:
        return query.order_by(
            desc(DailyPartsCalculationResult.calculation_date)
        ).first()


# Get latest daily error count
def get_latest_daily_parts_error_count(
    db: Session,
    erp_number: str,
    part_id: str,
    is_reverse: bool = False,
    specific_date: Optional[date] = None,
) -> Optional[DailyPartsErrorCount]:
    """
    Get the latest daily error count for a specific ERP and part ID.
    If specific_date is provided, get count for that date, otherwise get the most recent.

    :param db: Database session
    :param erp_number: ERP number
    :param part_id: Part ID
    :param is_reverse: Whether to get reverse comparison counts
    :param specific_date: Optional specific date to retrieve
    :return: DailyPartsErrorCount or None
    """
    query = db.query(DailyPartsErrorCount).filter(
        DailyPartsErrorCount.erp_number == erp_number,
        DailyPartsErrorCount.part_id == part_id,
        DailyPartsErrorCount.cal_type == 'reverse'
        if is_reverse
        else DailyPartsErrorCount.cal_type != 'reverse',
    )

    if specific_date:
        query = query.filter(DailyPartsErrorCount.create_date == specific_date)
        return query.first()
    else:
        return query.order_by(desc(DailyPartsErrorCount.create_date)).first()


# Get daily error summary for a specific date or the latest date
def get_daily_parts_error_summary(
    db: Session,
    erp_number: Optional[str] = None,
    is_reverse: bool = False,
    specific_date: Optional[date] = None,
) -> List[Dict]:
    """
    Get summary of daily error counts, optionally filtered by ERP and date.

    :param db: Database session
    :param erp_number: Optional ERP number to filter by
    :param is_reverse: Whether to get reverse comparison counts
    :param specific_date: Optional specific date to retrieve
    :return: List of error summary dictionaries
    """
    try:
        # 构建基础过滤条件
        base_filters = [
            DailyPartsErrorCount.erp_number != '',
            DailyPartsErrorCount.part_id != '',
        ]

        if erp_number:
            base_filters.append(DailyPartsErrorCount.erp_number == erp_number)

        if is_reverse:
            base_filters.append(DailyPartsErrorCount.cal_type == 'reverse')
        else:
            base_filters.append(DailyPartsErrorCount.cal_type != 'reverse')

        # 添加日期过滤条件
        if specific_date:
            base_filters.append(
                DailyPartsErrorCount.create_date == specific_date
            )
        else:
            # 如果没有指定日期，找出最新日期
            latest_date = (
                db.query(func.max(DailyPartsErrorCount.create_date))
                .filter(*base_filters)
                .scalar()
            )

            if isinstance(latest_date, str):
                latest_date = datetime.strptime(latest_date, '%Y-%m-%d').date()

            if latest_date:
                base_filters.append(
                    DailyPartsErrorCount.create_date == latest_date
                )
            else:
                # 如果没有找到有效日期，返回空列表
                return []

        # 为每个erp_number和part_id组合找出最新的create_time记录
        subquery = (
            db.query(
                DailyPartsErrorCount.erp_number,
                DailyPartsErrorCount.part_id,
                DailyPartsErrorCount.cal_type,
                func.max(DailyPartsErrorCount.create_time).label('max_time'),
            )
            .filter(*base_filters)
            .group_by(
                DailyPartsErrorCount.erp_number,
                DailyPartsErrorCount.part_id,
                DailyPartsErrorCount.cal_type,
            )
            .subquery()
        )

        # 构建最终查询，连接子查询获取完整记录
        final_query = (
            db.query(DailyPartsErrorCount)
            .join(
                subquery,
                and_(
                    DailyPartsErrorCount.erp_number == subquery.c.erp_number,
                    DailyPartsErrorCount.part_id == subquery.c.part_id,
                    DailyPartsErrorCount.cal_type == subquery.c.cal_type,
                    DailyPartsErrorCount.create_time == subquery.c.max_time,
                ),
            )
            .filter(*base_filters)
        )

        # 获取结果
        latest_records = final_query.all()

        # 转换为字典列表
        result = []
        for record in latest_records:
            if record.erp_number == '' or record.erp_number == '0000':
                continue
            result.append(
                {
                    'erp_number': record.erp_number,
                    'part_id': str(int(record.part_id)),
                    'error_count': record.error_count,
                    'total_count': record.total_count,
                    'create_time': record.create_time.isoformat()
                    if record.create_time
                    else None,
                    'create_date': record.create_date.strftime('%Y-%m-%d')
                    if record.create_date
                    else None,
                    'cal_type': record.cal_type,
                }
            )

        return result
    except Exception as e:
        raise Exception(f'Failed to get daily parts error summary: {str(e)}')


# Get all erp daily error summary
def get_all_erp_daily_error_summary(
    db: Session, is_reverse: bool = False, specific_date: Optional[date] = None
) -> Dict:
    """
    Get a summary of error statistics for all ERPs with daily data.

    :param db: Database session
    :param is_reverse: Whether to get reverse comparison data
    :param specific_date: Optional specific date to retrieve
    :return: Dictionary with ERP statistics
    """
    try:
        target_date = specific_date
        if not target_date:
            target_date = db.query(
                func.max(DailyPartsErrorCount.create_date)
            ).scalar()

            if not target_date:
                # 如果没有数据，返回空结果
                return {
                    'total_erps': 0,
                    'erp_stats': [],
                    'overall_summary': {
                        'total_parts': 0,
                        'total_errors': 0,
                        'error_rate': 0,
                        'last_update': datetime.now().isoformat(),
                    },
                }
        # First get all valid ERPs and parts from the inner_erp_framework_list
        base_query = text(
            """
            SELECT DISTINCT
                erp,
                partNumber
            FROM inner_erp_framework_list
            WHERE is_delete = 0
            ORDER BY erp, partNumber
        """
        )

        base_data = db.execute(base_query).fetchall()
        if not base_data:
            return {
                'total_erps': 0,
                'erp_stats': [],
                'overall_summary': {
                    'total_parts': 0,
                    'total_errors': 0,
                    'error_rate': 0,
                    'last_update': datetime.now().isoformat(),
                },
            }

        # Get error statistics from the daily table
        error_stats = get_daily_parts_error_summary(
            db, is_reverse=is_reverse, specific_date=target_date
        )
        error_lookup = {
            f"{stat['erp_number']}_{str(stat['part_id']).zfill(2)}": stat
            for stat in error_stats
        }

        # Remove debugging breakpoint

        # Group stats by ERP
        erp_stats = {}
        total_parts = 0

        for row in base_data:
            erp = row.erp
            part = str(row.partNumber).zfill(2)

            if erp not in erp_stats:
                erp_stats[erp] = {
                    'erp_number': erp,
                    'total_parts': 0,
                    'error_counts': 0,
                    'checked_parts': 0,
                    'total_counts': 0,
                    'last_update': None,
                    'parts_details': [],  # Add a new field for part details
                }

            erp_stats[erp]['total_parts'] += 1
            total_parts += 1

            # Find matching error statistics
            error_key = f'{erp}_{part}'
            if error_key in error_lookup:
                error_info = error_lookup[error_key]
                erp_stats[erp]['checked_parts'] += 1
                erp_stats[erp]['error_counts'] += error_info['error_count']
                erp_stats[erp]['total_counts'] += error_info['total_count']

                # Create part detail entry and add to parts_details
                part_detail = {
                    'part_id': str(int(part)),
                    'error_count': error_info['error_count'],
                    'total_count': error_info['total_count'],
                    'error_rate': (
                        round(
                            error_info['error_count']
                            / error_info['total_count']
                            * 100,
                            2,
                        )
                        if error_info['total_count'] > 0
                        else 0
                    ),
                    'last_update': error_info['create_time'],
                }
                erp_stats[erp]['parts_details'].append(part_detail)

                # Update last update time
                current_time = (
                    datetime.fromisoformat(error_info['create_time'])
                    if error_info['create_time']
                    else None
                )
                if current_time and (
                    not erp_stats[erp]['last_update']
                    or current_time
                    > datetime.fromisoformat(erp_stats[erp]['last_update'])
                ):

                    # Calculate the error rate for each ERP
                    for erp in erp_stats:
                        erp_stats[erp]['error_rate'] = (
                            round(
                                erp_stats[erp]['error_counts']
                                / erp_stats[erp]['total_counts']
                                * 100,
                                2,
                            )
                            if erp_stats[erp]['total_counts'] > 0
                            else 0
                        )

        # Convert dictionary to list for consistent return format
        erp_stats_list = list(erp_stats.values())

        return {
            'total_erps': len(erp_stats),
            'erp_stats': erp_stats_list,
            'date': specific_date.isoformat()
            if specific_date
            else (
                db.query(func.max(DailyPartsErrorCount.create_date))
                .scalar()
                .strftime('%Y-%m-%d')
                if db.query(
                    func.max(DailyPartsErrorCount.create_date)
                ).scalar()
                else None
            ),
        }

    except Exception as e:
        raise Exception(f'Failed to get all ERP daily error summary: {str(e)}')


# Get date range for which daily data is available
def get_available_daily_data_dates(db: Session) -> List[date]:
    """
    Get a list of dates for which daily comparison data is available

    :param db: Database session
    :return: List of dates sorted in descending order (newest first)
    """
    try:
        dates = (
            db.query(distinct(DailyPartsErrorCount.create_date))
            .order_by(desc(DailyPartsErrorCount.create_date))
            .all()
        )
        return [d[0] for d in dates]
    except Exception as e:
        raise Exception(f'Failed to get available daily data dates: {str(e)}')


# Get historical data for a specific ERP and part
def get_historical_daily_data(
    db: Session,
    erp_number: str,
    part_id: str,
    days: int = 30,
    is_reverse: bool = False,
) -> List[Dict]:
    """
    Get historical daily data for a specific ERP and part ID

    :param db: Database session
    :param erp_number: ERP number
    :param part_id: Part ID
    :param days: Number of days of history to retrieve
    :param is_reverse: Whether to get reverse comparison data
    :return: List of daily error statistics
    """
    try:
        # Calculate the date range
        end_date = (
            db.query(func.max(DailyPartsErrorCount.create_date)).scalar()
            or date.today()
        )
        start_date = end_date - timedelta(days=days)

        # Query daily records within the date range
        query = db.query(DailyPartsErrorCount).filter(
            DailyPartsErrorCount.erp_number == erp_number,
            DailyPartsErrorCount.part_id == part_id,
            DailyPartsErrorCount.create_date >= start_date,
            DailyPartsErrorCount.create_date <= end_date,
        )

        if is_reverse:
            query = query.filter(DailyPartsErrorCount.cal_type == 'reverse')
        else:
            query = query.filter(DailyPartsErrorCount.cal_type != 'reverse')

        query = query.order_by(asc(DailyPartsErrorCount.create_date))
        records = query.all()

        # Convert to dictionary list
        result = []
        for record in records:
            result.append(
                {
                    'erp_number': record.erp_number,
                    'part_id': record.part_id,
                    'error_count': record.error_count,
                    'total_count': record.total_count,
                    'create_date': record.create_date.isoformat()
                    if record.create_date
                    else None,
                    'error_rate': round(
                        record.error_count / record.total_count * 100, 2
                    )
                    if record.total_count
                    else 0,
                    'cal_type': record.cal_type,
                }
            )

        return result
    except Exception as e:
        raise Exception(f'Failed to get historical daily data: {str(e)}')


def get_all_airline_connectors(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    search: Optional[str] = None,
    sort_field: str = 'id',
    sort_order: str = 'asc',
) -> List[AirlineConnectorInfo]:
    """获取所有航插信息，支持分页、搜索和排序"""
    query = db.query(AirlineConnectorInfo)

    # 搜索功能
    if search:
        query = query.filter(
            (AirlineConnectorInfo.male_connector_code.ilike(f'%{search}%'))
            | (AirlineConnectorInfo.male_connector_name.ilike(f'%{search}%'))
            | (AirlineConnectorInfo.female_connector_code.ilike(f'%{search}%'))
            | (AirlineConnectorInfo.female_connector_name.ilike(f'%{search}%'))
        )

    # 排序
    if sort_order == 'asc':
        query = query.order_by(asc(getattr(AirlineConnectorInfo, sort_field)))
    else:
        query = query.order_by(desc(getattr(AirlineConnectorInfo, sort_field)))

    return query.offset(skip).limit(limit).all()


def get_total_connector_count(
    db: Session, search: Optional[str] = None
) -> int:
    """获取总记录数"""
    query = db.query(AirlineConnectorInfo)
    if search:
        query = query.filter(
            (AirlineConnectorInfo.male_connector_code.ilike(f'%{search}%'))
            | (AirlineConnectorInfo.male_connector_name.ilike(f'%{search}%'))
            | (AirlineConnectorInfo.female_connector_code.ilike(f'%{search}%'))
            | (AirlineConnectorInfo.female_connector_name.ilike(f'%{search}%'))
        )
    return query.count()


def get_airline_connector_by_id(
    db: Session, connector_id: int
) -> Optional[AirlineConnectorInfo]:
    """根据ID获取特定航插记录"""
    return (
        db.query(AirlineConnectorInfo)
        .filter(AirlineConnectorInfo.id == connector_id)
        .first()
    )


def create_airline_connector(
    db: Session,
    male_connector_code: str,
    male_connector_name: str,
    female_connector_code: str,
    female_connector_name: str,
    remark: Optional[str] = None,
) -> AirlineConnectorInfo:
    """创建新的航插记录"""
    db_connector = AirlineConnectorInfo(
        male_connector_code=male_connector_code,
        male_connector_name=male_connector_name,
        female_connector_code=female_connector_code,
        female_connector_name=female_connector_name,
        remark=remark,
    )
    db.add(db_connector)
    db.commit()
    db.refresh(db_connector)
    return db_connector


def update_airline_connector(
    db: Session,
    connector_id: int,
    male_connector_code: Optional[str] = None,
    male_connector_name: Optional[str] = None,
    female_connector_code: Optional[str] = None,
    female_connector_name: Optional[str] = None,
    remark: Optional[str] = None,
) -> Optional[AirlineConnectorInfo]:
    """更新现有航插记录"""
    db_connector = (
        db.query(AirlineConnectorInfo)
        .filter(AirlineConnectorInfo.id == connector_id)
        .first()
    )

    if db_connector:
        if male_connector_code is not None:
            db_connector.male_connector_code = male_connector_code
        if male_connector_name is not None:
            db_connector.male_connector_name = male_connector_name
        if female_connector_code is not None:
            db_connector.female_connector_code = female_connector_code
        if female_connector_name is not None:
            db_connector.female_connector_name = female_connector_name
        if remark is not None:
            db_connector.remark = remark

        db.commit()
        db.refresh(db_connector)

    return db_connector


def delete_airline_connector(db: Session, connector_id: int) -> bool:
    """删除指定航插记录"""
    db_connector = (
        db.query(AirlineConnectorInfo)
        .filter(AirlineConnectorInfo.id == connector_id)
        .first()
    )

    if db_connector:
        db.delete(db_connector)
        db.commit()
        return True
    return False


def create_part_note(
    db: Session,
    erp_number: str,
    part_id: str,
    note_content: str,
    note_content_reverse: str,
    username: str,
) -> BomPartNote:
    # First, deactivate any existing active notes for this part
    db.query(BomPartNote).filter(
        BomPartNote.erp_number == erp_number,
        BomPartNote.part_id == part_id,
        BomPartNote.is_active == True,
    ).update({BomPartNote.is_active: False})

    # Create new note
    new_note = BomPartNote(
        erp_number=erp_number,
        part_id=part_id,
        note_content=note_content,
        note_content_reverse=note_content_reverse,
        created_by=username,
        is_active=True,
        version=1,
    )

    db.add(new_note)
    db.commit()
    db.refresh(new_note)
    return new_note


def update_part_note(
    db: Session,
    note_id: int,
    new_content: str,
    new_content_reverse: str,
    username: str,
) -> BomPartNote:
    # Get the current note
    current_note = (
        db.query(BomPartNote)
        .filter(BomPartNote.id == note_id, BomPartNote.is_active == True)
        .first()
    )

    if not current_note:
        raise Exception('Note not found')

    # Create a new version with the updated content
    new_version = BomPartNote(
        erp_number=current_note.erp_number,
        part_id=current_note.part_id,
        note_content=new_content,
        new_content_reverse=new_content_reverse,
        created_by=current_note.created_by,
        created_at=current_note.created_at,
        updated_by=username,
        updated_at=datetime.now(),
        is_active=True,
        version=current_note.version + 1,
    )

    # Deactivate the current note
    current_note.is_active = False

    db.add(new_version)
    db.commit()
    db.refresh(new_version)
    return new_version


def get_latest_erp_notes(db: Session, erp_number: str) -> List[BomPartNote]:
    return (
        db.query(BomPartNote)
        .filter(
            BomPartNote.erp_number == erp_number, BomPartNote.is_active == True
        )
        .all()
    )


def get_part_note_history(
    db: Session, erp_number: str, part_id: str
) -> List[BomPartNote]:
    return (
        db.query(BomPartNote)
        .filter(
            BomPartNote.erp_number == erp_number,
            BomPartNote.part_id == part_id,
        )
        .order_by(BomPartNote.version.desc())
        .all()
    )


def delete_part_note(db: Session, note_id: int, username: str) -> bool:
    note = (
        db.query(BomPartNote)
        .filter(BomPartNote.id == note_id, BomPartNote.is_active == True)
        .first()
    )

    if not note:
        raise Exception('Note not found')

    # Mark as inactive but keep in history
    note.is_active = False
    note.updated_by = username
    note.updated_at = datetime.now()

    db.commit()
    return True


def reactivate_part_note(
    db: Session, history_note_id: int, username: str
) -> BomPartNote:
    """
    Reactivate a historical note version

    :param db: Database session
    :param history_note_id: ID of the historical note to reactivate
    :param username: Username of the user performing the reactivation
    :return: The reactivated note
    """
    # Get the historical note
    history_note = (
        db.query(BomPartNote).filter(BomPartNote.id == history_note_id).first()
    )

    if not history_note:
        raise Exception('Note not found')

    # First deactivate any currently active notes for this part
    db.query(BomPartNote).filter(
        BomPartNote.erp_number == history_note.erp_number,
        BomPartNote.part_id == history_note.part_id,
        BomPartNote.is_active == True,
    ).update({BomPartNote.is_active: False})

    # Create a new version based on the historical note
    new_version = BomPartNote(
        erp_number=history_note.erp_number,
        part_id=history_note.part_id,
        note_content=history_note.note_content,
        created_by=history_note.created_by,
        created_at=history_note.created_at,
        updated_by=username,
        updated_at=datetime.now(),
        is_active=True,
        # Get the latest version number and increment it
        version=db.query(func.max(BomPartNote.version))
        .filter(
            BomPartNote.erp_number == history_note.erp_number,
            BomPartNote.part_id == history_note.part_id,
        )
        .scalar()
        + 1,
    )

    db.add(new_version)
    db.commit()
    db.refresh(new_version)
    return new_version


def get_all_reverse_mapping(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    search: Optional[str] = None,
    sort_field: str = 'id',
    sort_order: str = 'asc',
) -> List[BomReverseMapping]:
    """获取所有航插信息，支持分页、搜索和排序"""
    query = db.query(BomReverseMapping)

    # 搜索功能
    if search:
        query = query.filter(
            (BomReverseMapping.inv_code.ilike(f'%{search}%'))
            | (BomReverseMapping.memo_cn.ilike(f'%{search}%'))
            | (BomReverseMapping.reverse_inv_code.ilike(f'%{search}%'))
            | (BomReverseMapping.reverse_memo_cn.ilike(f'%{search}%'))
        )

    # 排序
    if sort_order == 'asc':
        query = query.order_by(asc(getattr(BomReverseMapping, sort_field)))
    else:
        query = query.order_by(desc(getattr(BomReverseMapping, sort_field)))

    return query.offset(skip).limit(limit).all()


def get_total_reverse_mapping_count(
    db: Session, search: Optional[str] = None
) -> int:
    """获取总记录数"""
    query = db.query(BomReverseMapping)
    if search:
        query = query.filter(
            (BomReverseMapping.inv_code.ilike(f'%{search}%'))
            | (BomReverseMapping.memo_cn.ilike(f'%{search}%'))
            | (BomReverseMapping.reverse_inv_code.ilike(f'%{search}%'))
            | (BomReverseMapping.reverse_memo_cn.ilike(f'%{search}%'))
        )
    return query.count()


def get_reverse_mapping_by_id(
    db: Session, id: int
) -> Optional[BomReverseMapping]:
    """根据ID获取特定航插记录"""
    return (
        db.query(BomReverseMapping).filter(BomReverseMapping.id == id).first()
    )


def create_reverse_mapping(
    db: Session,
    inv_code: str,
    memo_cn: str,
    reverse_inv_code: str,
    reverse_memo_cn: str,
) -> BomReverseMapping:
    """创建新的航插记录"""
    db_connector = BomReverseMapping(
        inv_code=inv_code,
        memo_cn=memo_cn,
        reverse_inv_code=reverse_inv_code,
        reverse_memo_cn=reverse_memo_cn,
    )
    db.add(db_connector)
    db.commit()
    db.refresh(db_connector)
    return db_connector


def update_reverse_mapping(
    db: Session,
    id: int,
    inv_code: Optional[str] = None,
    memo_cn: Optional[str] = None,
    reverse_inv_code: Optional[str] = None,
    reverse_memo_cn: Optional[str] = None,
) -> Optional[BomReverseMapping]:
    """更新现有航插记录"""
    db_connector = (
        db.query(BomReverseMapping).filter(BomReverseMapping.id == id).first()
    )

    if db_connector:
        if inv_code is not None:
            db_connector.inv_code = inv_code
        if memo_cn is not None:
            db_connector.memo_cn = memo_cn
        if reverse_inv_code is not None:
            db_connector.reverse_inv_code = reverse_inv_code
        if reverse_memo_cn is not None:
            db_connector.reverse_memo_cn = reverse_memo_cn

        db.commit()
        db.refresh(db_connector)

    return db_connector


def delete_reverse_mapping(db: Session, id: int) -> bool:
    """删除指定航插记录"""
    db_connector = (
        db.query(BomReverseMapping).filter(BomReverseMapping.id == id).first()
    )

    if db_connector:
        db.delete(db_connector)
        db.commit()
        return True
    return False


def batch_upsert_reverse_mappings(
    db: Session, mappings: List[Dict]
) -> Dict[str, int]:
    """
    批量创建或更新正反机映射记录的优化版本。
    使用批量查询减少数据库访问次数。

    :param db: SQLAlchemy数据库会话
    :param mappings: 包含映射数据的字典列表
    :return: 包含操作统计的字典 {'created': 创建数量, 'updated': 更新数量, 'total': 总数量}
    """
    if not mappings:
        return {'created': 0, 'updated': 0, 'total': 0}

    try:
        # 提取所有inv_code
        inv_codes = [
            record.get('inv_code')
            for record in mappings
            if record.get('inv_code')
        ]

        if not inv_codes:
            return {'created': 0, 'updated': 0, 'total': 0}

        # 批量查询现有记录
        existing_mappings = (
            db.query(BomReverseMapping)
            .filter(BomReverseMapping.inv_code.in_(inv_codes))
            .all()
        )

        # 创建现有记录的字典以便快速查找
        existing_dict = {
            mapping.inv_code: mapping for mapping in existing_mappings
        }

        created_count = 0
        updated_count = 0
        new_mappings = []

        for record in mappings:
            inv_code = record.get('inv_code')
            if not inv_code:
                continue

            if str(inv_code) in existing_dict:
                # 更新现有记录
                existing_mapping = existing_dict[str(inv_code)]
                if record.get('memo_cn') is not None:
                    existing_mapping.memo_cn = record.get('memo_cn')
                if record.get('reverse_inv_code') is not None:
                    existing_mapping.reverse_inv_code = record.get(
                        'reverse_inv_code'
                    )
                if record.get('reverse_memo_cn') is not None:
                    existing_mapping.reverse_memo_cn = record.get(
                        'reverse_memo_cn'
                    )

                updated_count += 1
            else:
                # 创建新记录
                new_mapping = BomReverseMapping(
                    inv_code=inv_code,
                    memo_cn=record.get('memo_cn'),
                    reverse_inv_code=record.get('reverse_inv_code'),
                    reverse_memo_cn=record.get('reverse_memo_cn'),
                )
                new_mappings.append(new_mapping)
                created_count += 1

        # 批量添加新记录
        if new_mappings:
            db.add_all(new_mappings)

        # 提交所有更改
        db.commit()

        return {
            'created': created_count,
            'updated': updated_count,
            'total': created_count + updated_count,
        }

    except Exception as e:
        # 如果发生错误，回滚事务以保持数据一致性
        db.rollback()
        # 重新抛出异常，以便上层API可以捕获它
        raise e


# ==================== 物料过滤规则相关操作 ====================


def get_all_material_filter_rules(db: Session) -> List[MaterialFilterRule]:
    """获取所有活跃的物料过滤规则"""
    return (
        db.query(MaterialFilterRule)
        .filter(MaterialFilterRule.is_active == True)
        .order_by(MaterialFilterRule.description)
        .all()
    )


def get_material_filter_rule_by_id(
    db: Session, rule_id: int
) -> Optional[MaterialFilterRule]:
    """根据ID获取物料过滤规则"""
    return (
        db.query(MaterialFilterRule)
        .filter(
            MaterialFilterRule.id == rule_id,
            MaterialFilterRule.is_active == True,
        )
        .first()
    )


def create_material_filter_rule(
    db: Session, description: str, is_included: bool
) -> MaterialFilterRule:
    """创建新的物料过滤规则"""
    # 检查是否已存在相同描述的规则
    existing_rule = (
        db.query(MaterialFilterRule)
        .filter(
            MaterialFilterRule.description == description,
            MaterialFilterRule.is_active == True,
        )
        .first()
    )

    if existing_rule:
        raise ValueError(f"物料描述 '{description}' 的过滤规则已存在")

    new_rule = MaterialFilterRule(
        description=description, is_included=is_included
    )
    db.add(new_rule)
    db.commit()
    db.refresh(new_rule)
    return new_rule


def update_material_filter_rule(
    db: Session,
    rule_id: int,
    description: str = None,
    is_included: bool = None,
    is_active: bool = None,
) -> Optional[MaterialFilterRule]:
    """更新物料过滤规则"""
    rule = (
        db.query(MaterialFilterRule)
        .filter(
            MaterialFilterRule.id == rule_id,
        )
        .first()
    )

    if not rule:
        return None

    # 如果要更新描述，检查是否与其他规则冲突
    if description and description != rule.description:
        existing_rule = (
            db.query(MaterialFilterRule)
            .filter(
                MaterialFilterRule.description == description,
                MaterialFilterRule.is_active == True,
                MaterialFilterRule.id != rule_id,
            )
            .first()
        )

        if existing_rule:
            raise ValueError(f"物料描述 '{description}' 的过滤规则已存在")

        rule.description = description

    if is_included is not None:
        rule.is_included = is_included

    if is_active is not None:
        rule.is_active = is_active

    rule.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(rule)
    return rule


def delete_material_filter_rule(db: Session, rule_id: int) -> bool:
    """删除物料过滤规则（软删除）"""
    rule = (
        db.query(MaterialFilterRule)
        .filter(
            MaterialFilterRule.id == rule_id,
            MaterialFilterRule.is_active == True,
        )
        .first()
    )

    if not rule:
        return False

    rule.is_active = False
    rule.updated_at = datetime.now(timezone.utc)
    db.commit()
    return True


def get_material_filter_rules_by_type(
    db: Session, is_included: bool
) -> List[MaterialFilterRule]:
    """根据包含/排除类型获取物料过滤规则"""
    return (
        db.query(MaterialFilterRule)
        .filter(
            MaterialFilterRule.is_included == is_included,
            MaterialFilterRule.is_active == True,
        )
        .order_by(MaterialFilterRule.description)
        .all()
    )
