from fastapi import (
    FastAPI,
    Request,
)
import os
from fastapi.staticfiles import <PERSON>aticFiles
import uvicorn
from typing import Dict
from fastapi.middleware.cors import CORSMiddleware
from collections import OrderedDict

from jwts import parse_userid
from core.logger import logger
from routers import (
    session,
    statistics,
    common,
    parts,
    daily_parts,
    results,
    error_reasons,
    machine_type_material,
    calculation,
    airline_connector,
    reverse_mapping,
    # task_manager,
)

from core.database import init_db

init_db()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allow_headers=['*'],
)


@app.middleware('http')
async def login_check_middleware(request: Request, call_next):
    if request.method != 'OPTIONS':
        access_token = request.headers.get('Authorization', '')
        access_token = access_token.replace('Bearer ', '')

        user_id = parse_userid(access_token)

        # if user_id is None:
        #     return JSONResponse(status_code=401, content={"code": 401, "msg": "长时间未操作"})

        # 将 user_id 存储在请求状态中
        request.state.user_id = user_id

    response = await call_next(request)
    return response


logger.debug('CORS middleware configured')

# 使用 OrderedDict 来存储会话，限制最多 100 个
sessions: OrderedDict[str, Dict] = OrderedDict()
MAX_SESSIONS = 1000000

# 注册路由
app.include_router(session.router)
app.include_router(calculation.router)
app.include_router(results.router)
app.include_router(statistics.router)
app.include_router(error_reasons.router)
app.include_router(parts.router)
app.include_router(daily_parts.daily_router)
app.include_router(common.router)
app.include_router(machine_type_material.router)
app.include_router(airline_connector.router)
app.include_router(reverse_mapping.router)
# app.include_router(task_manager.task_router)

# # 启动定时任务
# from scheduled_tasks import start_scheduled_tasks
# start_scheduled_tasks()

# 挂载前端静态文件
app.mount('/', StaticFiles(directory='./dist'), name='static')
logger.debug('Static files mounted')

if __name__ == '__main__':
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 8080))
    logger.info(f'Starting application on {host}:{port}')
    uvicorn.run('main:app', host=host, port=port, reload=False)
